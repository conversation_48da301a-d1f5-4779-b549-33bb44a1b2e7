(function () {
    const scriptTag = document.querySelector('script[data-user-id]');
    const userID = scriptTag.getAttribute('data-user-id');

    const pageURl = window.location.origin + window.location.pathname;
    const SERVER_URL = "https://yash-drf.abun.com";

    // Fetch the JavaScript code from the server
    fetch(SERVER_URL + '/api/frontend/load-tools-scripts/?user-id=' + encodeURIComponent(userID) + '&url=' + encodeURIComponent(pageURl))
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.text();
        })
        .then(scriptContent => {
            // Create a script element and inject the IIFE code directly
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.textContent = scriptContent;
            document.body.appendChild(script);
        })
        .catch(error => {
            console.error('Error loading tools scripts:', error);
        });
})();
