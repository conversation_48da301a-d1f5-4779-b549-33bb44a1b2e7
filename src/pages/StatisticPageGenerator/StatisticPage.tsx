
import { useMutation, useQuery } from '@tanstack/react-query';
import React, { useRef, useState, useEffect, useCallback } from 'react';
import './StatisticPageGenerator.min.css';
import { useLocation, useNavigate, useParams, useRouteLoaderData } from "react-router-dom";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { Player } from "@lottiefiles/react-lottie-player";
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import { BasePageData } from "../../pages/Base/Base";
import {
  generateStatIdeasMutation,
  generateAIStatsPageMutation,
  modifyAIStatsPageMutation,
  getTaskProgress,
  setCurrentStatsVersionFn,
  deleteStatsPageVersionMutation,
  getAIStatsPagesQuery,
  makeApiRequest,
  getAIStatsPageDataQuery,
} from "../../utils/api";
import { pageURL } from "../routes";
import "aieditor/dist/style.css";
import AbunModal from "../../components/AbunModal/AbunModal";
import { Tooltip } from 'react-tooltip';
import GenericButton from '../../components/GenericButton/GenericButton';


interface StatsDataType {
  stats_id: string;
  html_content: string;
  stats_type: string;
  stats_topic: string;
  stats_description: string;
  current_version_id?: number;
}

interface StatsPageApiResponse {
  data: {
    stats_data: StatsDataType & {
      versions?: StatsVersion[];
    };
  };
  status?: string;
  message?: string;
}

interface StatsPageListApiResponse {
  status: "success" | "error";
  data: {
    pages: any[];
    stats_pages_generated: number;
    max_stats_pages_allowed: number;
  };
  message?: string;
}

interface LocationState {
  statsId?: string;
  statsType?: string;
  statsTopic?: string;
  userModifications?: string[];
  fromOtherTopic?: boolean;
  fromExisting?: boolean;
  customTitle?: string;
  keyword?: string;
  fromCustomTitle?: boolean;
  statsData?: any; // Data passed from StatsPageTable to avoid redundant API calls
  navigationTimestamp?: number;
}

interface StatsVersion {
  id: number;
  version_name: string;
  html_code: string;
  changes_summary?: string;
  created_on: string;
  created_on_relative: string;
  code_length: number;
  code_preview: string;
}


declare global {
  interface Window {
    AbunStatsWidget?: {
      [statsId: string]: {
        refresh: () => void;
      };
    };
  }
}


const StatisticPage = () => {


  const basePageData = useRouteLoaderData("base") as BasePageData;

  const { active_website_domain } = basePageData;
  const { currentPlanName } = basePageData;

  // --------------------------- HOOKS ---------------------------
  const location = useLocation();
  const navigate = useNavigate();
  const state = location.state as LocationState;
  const iframeRef = useRef<HTMLIFrameElement>(null);


  // --------------------------- STATES ---------------------------
  const [keyword, setKeyword] = useState('');
  const [idea, setIdea] = useState('');
  const [ideas, setIdeas] = useState<string[]>([]);
  const [fullIdeas, setFullIdeas] = useState<string[]>([]);
  const [showStatisticsPage, setShowStatisticsPage] = useState(false);
  const [statsData, setStatsData] = useState<StatsDataType | null>(null);
  const [updateTabOpen, setUpdateTabOpen] = useState(true);
  const [versionsTabOpen, setVersionsTabOpen] = useState(false);
  const [embedTabOpen, setEmbedTabOpen] = useState(false);
  const [userInput, setUserInput] = useState<string>("");
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [versionToDelete, setVersionToDelete] = useState<number | null>(null);
  const isUpdatingRef = useRef(false);
  const [scriptTag, setScriptTag] = useState('');
  const [divTag, setDivTag] = useState('');
  const [scriptGenerated, setScriptGenerated] = useState(false);
  const [statsId, setStatsId] = useState<string>(state?.statsId || '');
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [statsType, setStatsType] = useState<string>(state?.statsType || '');
  const [versions, setVersions] = useState<StatsVersion[]>([]);
  const [currentVersionId, setCurrentVersionId] = useState<number | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const { taskId } = useParams<{ taskId?: string }>();
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(taskId || null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // --------------------------- REFS ---------------------------
  const errorAlertRef = useRef<any>(null);
  const successAlertRef = useRef<any>(null);

  // --------------------------- MUTATIONS ---------------------------
  const generateStatIdeasMut = useMutation(generateStatIdeasMutation);
  const generateAIStatsPageMut = useMutation(generateAIStatsPageMutation);


  // --------------------------- QUERIES ---------------------------
  // Query for fetching stats page data when editing existing page
  // Only fetch if we don't already have the data from navigation state
  const {
    data: statsPageData,
    isLoading: isLoadingStatsPage,
    error: _
  } = useQuery({
    ...getAIStatsPageDataQuery(state?.statsId || ''),
    enabled: !!state?.statsId && state.statsId.trim() !== '' && !state?.statsData,
  });

  const {
    data: statsPageListData,
    isLoading: isLoadingStatsList,
    error: statsListError
  } = useQuery<StatsPageListApiResponse>(getAIStatsPagesQuery());


  const [statsPageLimits, setStatsPageLimits] = useState({
    generated: 0,
    maxAllowed: 0
  });

  const modifyAIStatsPageMut = useMutation({
    ...modifyAIStatsPageMutation
  });

  // Determine if any mutation is loading
  const isLoading = (state?.statsId && isLoadingStatsPage) || generateStatIdeasMut.isLoading || generateAIStatsPageMut.isLoading || modifyAIStatsPageMut.isLoading || isGenerating || isUpdating;

  // --------------------------- HANDLERS ---------------------------
  const handleGenerateIdea = () => {
    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if (!keyword.trim()) {
      errorAlertRef.current?.show("Please enter a keyword");
      return;
    }

    generateStatIdeasMut.mutate(
      { keyword: keyword.trim() },
      {
        onSuccess: (response) => {
          const data = response.data;

          if (data && data.ideas) {
            const fullIdeasData = data.ideas;

            setFullIdeas(fullIdeasData);

            const cleanTitle = (title) => {
              return title
                .replace(/^\d+\.\s*/, '')
                .replace(/^\*+\s*/, '')
                .replace(/\s*\*+$/, '')
                .replace(/\*+/g, '')
                .trim();
            };

            const cleanedTitles = fullIdeasData.map(idea =>
              cleanTitle(typeof idea === 'object' ? idea.title : idea)
            );

            setIdeas(cleanedTitles);

          } else {
            errorAlertRef.current?.show("No ideas received from server");
          }
        },
        onError: (error) => {
          errorAlertRef.current?.show("Failed to generate ideas. Please try again.");
        }
      }
    );
  };

  const handleIdeaClick = (selectedTitle: string, index: number) => {
    setIdea(selectedTitle);
  };

  const canGenerateMore = (statsPageListData?.data?.stats_pages_generated || 0) < (statsPageListData?.data?.max_stats_pages_allowed || 5);

  const pollTaskProgress = (taskId: string, statsId: string, isForGeneration: boolean = true) => {
    const interval = setInterval(() => {
      getTaskProgress(taskId)
        .then((res) => {
          const status = res.data.status;
          console.log('Task status:', status);

          if (status === "success") {
            console.log("Task completed successfully");
            clearInterval(interval);

            fetchStatsData(statsId, isForGeneration);

          } else if (status === "failure") {
            clearInterval(interval);

            const errorMessage = res.data.progress_info?.error_message ||
              res.data.progress_info?.message ||
              "Task failed. Please try again.";

            handleTaskFailure({
              message: errorMessage,
              error_code: res.data.progress_info?.error_code || "TASK_FAILED"
            }, isForGeneration);

          } else if (status === "processing") {
            // Handle progress updates
            const progressInfo = res.data.progress_info;
            if (progressInfo?.current && progressInfo?.total) {
              console.log(`Progress: ${progressInfo.current}/${progressInfo.total}`);
            } else if (progressInfo?.status) {
              console.log(`Status: ${progressInfo.status}`);
            }
          }
        })
        .catch((err) => {
          console.error("Error fetching task progress:", err);
          clearInterval(interval);

          const isNetworkError = !err.response;
          const errorMessage = isNetworkError
            ? "Network error - please check your connection"
            : err.response?.data?.message || "Failed to fetch task progress";

          handleTaskFailure({
            message: errorMessage,
            error_code: isNetworkError ? "NETWORK_ERROR" : "API_ERROR"
          }, isForGeneration);
        });
    }, 2000);

    return interval;
  };

  const fetchStatsData = async (statsId: string, isForGeneration: boolean) => {
    try {
      const response = await makeApiRequest(`/api/frontend/get-stats-page-data/?stats_id=${statsId}`, 'get');
      const data = response.data;

      if (data.status === 'success') {
        if (isForGeneration) {
          handleGenerationSuccess(data);
        }
      } else {
        handleTaskFailure({
          message: data.message || "Failed to fetch stats data",
          error_code: "DATA_FETCH_ERROR"
        }, isForGeneration);
      }
    } catch (error) {
      console.error("Error fetching stats data:", error);
      handleTaskFailure({
        message: "Failed to fetch stats data after task completion",
        error_code: "DATA_FETCH_ERROR"
      }, isForGeneration);
    }
  };

  const handleTaskFailure = (data: any, isForGeneration: boolean) => {
    if (isForGeneration) {
      setIsGenerating(false);
      navigate(pageURL.staticPageGenerator, { replace: true });
    } else {
      setIsUpdating(false);
    }

    setCurrentTaskId(null);

    const errorMessage = data?.message || data?.error_message || "Task failed. Please try again.";
    errorAlertRef.current?.show(errorMessage);
  };

  const handleGenerationSuccess = (data: any) => {
    setIsGenerating(false);
    setCurrentTaskId(null);

    // Validate required data
    if (!data?.stats_id) {
      console.error('Missing stats_id in success data:', data);
      errorAlertRef.current?.show("Incomplete data received from server: missing stats_id");
      navigate(pageURL.staticPageGenerator, { replace: true });
      return;
    }

    // html_content should be available now from separate fetch
    if (!data?.html_content) {
      console.error('Missing html_content in success data:', data);
      errorAlertRef.current?.show("Incomplete data received from server: missing html_content");
      navigate(pageURL.staticPageGenerator, { replace: true });
      return;
    }

    setStatsId(data.stats_id);
    setHtmlContent(data.html_content);
    setStatsType(data.stats_type || '');

    const statsDataObj: StatsDataType = {
      stats_id: data.stats_id,
      html_content: data.html_content,
      stats_type: data.stats_type || '',
      stats_topic: data.stats_topic || '',
      stats_description: data.stats_description || ''
    };

    setStatsData(statsDataObj);
    setShowStatisticsPage(true);

    navigate(`${pageURL.staticPageGenerator}/${data.stats_id}`, { replace: true });

    // Handle version data if available
    if (data.version_id) {
      const initialVersion: StatsVersion = {
        id: data.version_id,
        version_name: data.version_name || `${data.stats_type}-v1`,
        html_code: data.html_content,
        changes_summary: "Initial version created",
        created_on: new Date().toISOString(),
        created_on_relative: "Just now",
        code_length: data.html_content.length,
        code_preview: data.html_content.substring(0, 200)
      };

      setVersions([initialVersion]);
      setCurrentVersionId(data.version_id);
    }

    successAlertRef.current?.show("Statistics page generated successfully!");
  };

  const handleGenerateStatisticPage = useCallback(() => {
    if (!canGenerateMore) {
      errorAlertRef.current?.show(
        `You have reached your monthly limit of ${statsPageListData?.data?.max_stats_pages_allowed || 5} stats pages. Please upgrade your plan to generate more.`
      );
      return;
    }

    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if (!idea.trim()) {
      errorAlertRef.current?.show("Please enter a statistics page idea");
      return;
    }

    const selectedIdeaIndex = ideas.indexOf(idea.trim());

    let extractedDescription = '';
    if (fullIdeas && Array.isArray(fullIdeas) && fullIdeas.length > 0 &&
      selectedIdeaIndex >= 0 && selectedIdeaIndex < fullIdeas.length) {

      const selectedFullIdea = fullIdeas[selectedIdeaIndex];

      if (selectedFullIdea &&
        typeof selectedFullIdea === 'object' &&
        selectedFullIdea !== null &&
        'description' in selectedFullIdea &&
        typeof (selectedFullIdea as any).description === 'string') {
        extractedDescription = (selectedFullIdea as any).description.trim();
      }
    }

    generateAIStatsPageMut.mutate(
      {
        stats_topic: idea.trim(),
        stats_description: extractedDescription,
        original_keyword: keyword || '',
        selected_idea: selectedIdeaIndex >= 0 ? fullIdeas[selectedIdeaIndex] : {}
      },
      {
        onSuccess: (response) => {
          const data = response.data;

          if (data.status === 'rejected' && data.reason === 'max_limit_reached') {
            errorAlertRef.current?.show(
              `You have reached your maximum limit of ${data.current_usage}/${data.max_allowed} stats pages for this plan. Please upgrade!`
            );
            return;
          }

          if (data && (data.status === 'success' || data.status === 'processing')) {
            if (data.task_id && data.stats_id) {
              setCurrentTaskId(data.task_id);

              const tempStatsData = {
                stats_id: data.stats_id,
                html_content: '', // Will be populated after task completion
                stats_type: data.stats_type || 'generating',
                stats_topic: data.stats_topic || idea.trim(),
                stats_description: data.stats_description || extractedDescription || ''
              };

              setStatsId(data.stats_id);
              setStatsType(data.stats_type || 'generating');
              setStatsData(tempStatsData);
              setShowStatisticsPage(true);
              setIsGenerating(true);

              const newURL = `${pageURL['staticPageGenerator']}/${data.stats_id}`;
              navigate(newURL, { replace: true });

              successAlertRef.current?.show("Statistics page generation started...");
              // Polling will start automatically via useEffect

            } else {
              console.error('Missing task_id or stats_id in response:', data);
              errorAlertRef.current?.show("Server response missing required data");
            }
          } else {
            errorAlertRef.current?.show("Unexpected response from server");
          }
        },
        onError: (error: any) => {
          setIsGenerating(false);
          setCurrentTaskId(null);
          setShowStatisticsPage(false);

          navigate(pageURL.staticPageGenerator, { replace: true });

          let errorMessage = "Failed to generate statistics page. Please try again.";
          if (error?.response?.data?.message) {
            errorMessage = error.response.data.message;
          } else if (error?.message) {
            errorMessage = error.message;
          }
          errorAlertRef.current?.show(errorMessage);
        }
      }
    );
  }, [canGenerateMore, statsPageListData, idea, keyword, ideas, fullIdeas, generateAIStatsPageMut, navigate]);

  // Add useEffect to start polling when currentTaskId is set
  useEffect(() => {
    if (currentTaskId && statsId) {
      console.log('Starting polling for task:', currentTaskId, 'stats_id:', statsId);
      const interval = pollTaskProgress(currentTaskId, statsId, true);

      return () => {
        if (interval) {
          clearInterval(interval);
        }
      };
    }
  }, [currentTaskId, statsId]);

  // CRITICAL FIX: Update handleUpdateStatsPage to use current HTML content
  const handleUpdateStatsPage = () => {
    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if (!userInput.trim() || !statsId) {
      errorAlertRef.current?.show("Please enter modifications and ensure stats page exists");
      return;
    }

    setIsUpdating(true);

    let currentHtmlContent = htmlContent;

    if (iframeRef.current) {
      const iframe = iframeRef.current;
      const doc = iframe.contentDocument || iframe.contentWindow?.document;
      if (doc) {
        currentHtmlContent = doc.documentElement.outerHTML;
        // Update state with current iframe content
        setHtmlContent(currentHtmlContent);
      }
    }

    modifyAIStatsPageMut.mutate(
      {
        stats_id: statsId,
        modifications: userInput.trim(),
        html_content: currentHtmlContent
      },
      {
        onSuccess: (response) => {
          const data = response.data;

          if (data && data.status === 'processing' && data.task_id) {
            // TODO: Use checkJobStatus
          } else {
            setIsUpdating(false);
            errorAlertRef.current?.show("Failed to start modification task");
          }
        },
        onError: (error: any) => {
          setIsUpdating(false);

          let errorMessage = "Failed to update statistics page.";

          if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
            errorMessage = "Request timed out. The server might still be processing. Please wait a moment and check if the update was applied.";
          } else if (error.message?.includes('broken pipe')) {
            errorMessage = "Connection was interrupted. Please try again.";
          } else if (error.response?.status === 404) {
            errorMessage = "API endpoint not found. Make sure DEBUG mode is enabled.";
          } else if (error.response?.data?.err_id === 'NO_STATS_PAGE_FOUND') {
            errorMessage = "Statistics page not found. Please generate a new one.";
          } else if (error.response?.data?.message) {
            errorMessage = error.response.data.message;
          }

          errorAlertRef.current?.show(errorMessage);
        }
      }
    );
  };

  const handleSaveDirectHtmlChanges = () => {
    errorAlertRef.current?.close();
    successAlertRef.current?.close();

    if (!statsId) {
      errorAlertRef.current?.show("Stats page not found");
      return;
    }

    let currentHtmlContent = htmlContent;

    if (iframeRef.current) {
      const iframe = iframeRef.current;
      const doc = iframe.contentDocument || iframe.contentWindow?.document;
      if (doc) {
        currentHtmlContent = doc.documentElement.outerHTML;
      }
    }

    if (!currentHtmlContent.trim()) {
      errorAlertRef.current?.show("No content to save");
      return;
    }

    modifyAIStatsPageMut.mutate(
      {
        stats_id: statsId,
        modifications: "Direct HTML content update",
        html_content: currentHtmlContent.trim()
      },
      {
        onSuccess: (response) => {
          const data = response.data;

          if (data && data.status === 'processing' && data.task_id) {
            // TODO: Use checkJobStatus
          } else {
            errorAlertRef.current?.show("Failed to start modification task");
          }
        },
        onError: (error) => {
          errorAlertRef.current?.show("Failed to save changes");
        }
      }
    );
  };

  const lastKnownContent = useRef('');

  useEffect(() => {
    if (iframeRef.current && htmlContent && !isUpdatingRef.current) {
      const iframe = iframeRef.current;
      const doc = iframe.contentDocument || iframe.contentWindow?.document;

      if (doc) {
        const currentDocContent = doc.documentElement.outerHTML;

        if (currentDocContent !== htmlContent && lastKnownContent.current !== htmlContent) {
          console.log('Updating iframe with new content');
          doc.open();
          doc.write(htmlContent);
          doc.close();
          lastKnownContent.current = htmlContent;
        }

        if (doc.body && !isUpdating && !modifyAIStatsPageMut.isLoading) {
          doc.body.contentEditable = 'true';

          const existingListener = doc.body.getAttribute('data-listener-added');
          if (!existingListener) {
            doc.body.setAttribute('data-listener-added', 'true');

            let updateTimeout;
            doc.body.addEventListener('input', (e) => {
              clearTimeout(updateTimeout);

              updateTimeout = setTimeout(() => {
                if (!isUpdatingRef.current) {
                  isUpdatingRef.current = true;
                  const newContent = doc.documentElement.outerHTML;

                  if (newContent !== lastKnownContent.current) {
                    setHtmlContent(newContent);
                    lastKnownContent.current = newContent;
                    console.log('Content updated from iframe editing');
                  }

                  setTimeout(() => {
                    isUpdatingRef.current = false;
                  }, 100);
                }
              }, 500);
            });
          }
        } else if (doc.body) {
          doc.body.contentEditable = 'false';
        }
      }
    }
  }, [htmlContent, isUpdating, modifyAIStatsPageMut.isLoading]);

  const handleTaskSuccess = (data) => {
    console.log('Task success data:', data);

    setIsUpdating(false);
    setUserInput('');

    if (data.html_content) {
      console.log('Updating HTML content with new version');
      setHtmlContent(data.html_content);
      lastKnownContent.current = data.html_content;
    }

    if (data.version_id && data.version_name) {
      const newVersion: StatsVersion = {
        id: data.version_id,
        version_name: data.version_name,
        html_code: data.html_content,
        changes_summary: data.changes_summary || data.modifications_applied || 'Modifications applied',
        created_on: new Date().toISOString(),
        created_on_relative: "Just now",
        code_length: data.html_content ? data.html_content.length : 0,
        code_preview: data.html_content ? data.html_content.substring(0, 200) : ''
      };

      setVersions(prev => {
        const filteredVersions = prev.filter(v => v.id !== data.version_id);
        return [newVersion, ...filteredVersions];
      });

      setCurrentVersionId(data.version_id);

      if (statsData) {
        const updatedStatsData: StatsDataType = {
          ...statsData,
          html_content: data.html_content,
          current_version_id: data.version_id
        };
        setStatsData(updatedStatsData);
        console.log('Updated statsData with new content');
      }
    }

    successAlertRef.current?.show(
      `Stats page updated successfully! ${data.changes_summary || data.modifications_applied || 'Modifications applied.'}`
    );
  };

  useEffect(() => {
    if (statsPageListData?.data) {
      setStatsPageLimits({
        generated: statsPageListData.data.stats_pages_generated || 0,
        maxAllowed: statsPageListData.data.max_stats_pages_allowed || 0
      });
    }
  }, [statsPageListData]);

  useEffect(() => {
    if (state?.fromOtherTopic && state?.statsId) {
      setStatsId(state.statsId);
      setStatsType(state.statsType || '');
      setIdea(state.statsTopic || '');

    } else if (state?.fromExisting && state?.statsId) {
      setStatsId(state.statsId);
      setStatsType(state.statsType || '');
      setIdea(state.statsTopic || '');

    } else if (state?.fromCustomTitle && state?.customTitle) {
      setIdea(state.customTitle);
      setKeyword(state.keyword || '');

      setTimeout(() => {
        handleGenerateStatisticPage();
      }, 500);
    }
  }, [state, handleGenerateStatisticPage]);

  useEffect(() => {
    if (taskId && !currentTaskId) {
      setCurrentTaskId(taskId);
    }
  }, [taskId, currentTaskId]);

  const handleTaskError = (data) => {
    setIsUpdating(false);
    errorAlertRef.current?.show(
      data.error_message || "Failed to update statistics page"
    );
  };

  const switchToVersion = async (versionId: number) => {
    const selectedVersion = versions.find(v => v.id === versionId);
    if (!selectedVersion || !statsData) {
      return;
    }

    try {
      console.log('Switching to version:', versionId, 'with content length:', selectedVersion.html_code.length);

      setCurrentVersionId(versionId);
      setHtmlContent(selectedVersion.html_code);
      lastKnownContent.current = selectedVersion.html_code; // Update ref

      const updatedStatsData: StatsDataType = {
        ...statsData,
        html_content: selectedVersion.html_code,
        current_version_id: versionId
      };
      setStatsData(updatedStatsData);

      // Sync with backend
      const response = await setCurrentStatsVersionFn(statsData.stats_id, versionId);

      if (response.status !== 200 && response.data?.status !== 'success') {
        console.error('Backend update failed:', response);
      }
    } catch (error) {
      console.error('Failed to switch version:', error);
    }
  };

  useEffect(() => {
    if (currentVersionId && versions.length > 0) {
      const savedVersion = versions.find(v => v.id === currentVersionId);
      if (savedVersion && savedVersion.html_code !== htmlContent) {
        // Only update if content is actually different
        setHtmlContent(savedVersion.html_code);
      }
    }
  }, [currentVersionId, versions]);

  // Helper function to process stats data (from API or navigation state)
  const processStatsData = (data: any, source: 'api' | 'navigation') => {
    const {
      stats_id,
      html_content,
      stats_type,
      stats_topic,
      stats_description,
      versions,
      current_version_id
    } = data;

    const statsDataObj: StatsDataType = {
      stats_id,
      html_content,
      stats_type,
      stats_topic,
      stats_description,
      current_version_id
    };

    setStatsId(stats_id);
    setStatsType(stats_type);
    setStatsData(statsDataObj);
    setIdea(stats_topic);
    setShowStatisticsPage(true);

    if (versions && Array.isArray(versions) && versions.length > 0) {
      setVersions(versions);

      const versionToSet = current_version_id || versions[0].id;
      setCurrentVersionId(versionToSet);

      const initialVersion = versions.find(v => v.id === versionToSet);
      if (initialVersion) {
        setHtmlContent(initialVersion.html_code);
      } else {
        setHtmlContent(html_content);
      }
    } else {
      console.warn('No versions found in response');
      setVersions([]);
      setCurrentVersionId(null);
      setHtmlContent(html_content);
    }

    if (state?.fromOtherTopic) {
      setTimeout(() => {
        successAlertRef.current?.show("New statistics page created and loaded successfully!");
      }, 500);
    } else if (state?.fromExisting) {
      setTimeout(() => {
        successAlertRef.current?.show(`Statistics page loaded successfully! ${source === 'navigation' ? '(from cache)' : ''}`);
      }, 500);
    }
  };

  // Handle data from navigation state (when coming from StatsPageTable)
  useEffect(() => {
    if (state?.statsData && state?.fromExisting) {
      console.log('Using stats data from navigation state (avoiding redundant API call)');
      processStatsData(state.statsData, 'navigation');
    }
  }, [state?.statsData, state?.fromExisting]);

  // Handle data from API query (when no navigation state data available)
  useEffect(() => {
    if (statsPageData && !state?.statsData) {
      console.log('Using stats data from API query');
      const response = statsPageData as StatsPageApiResponse;

      if (response?.data?.stats_data) {
        processStatsData(response.data.stats_data, 'api');
      } else {
        console.error('Invalid data structure received:', response);
        errorAlertRef.current?.show("Invalid data structure received from server");
      }
    }
  }, [statsPageData, state?.statsData]);


  const deleteStatsPageVersionMut = useMutation({
    ...deleteStatsPageVersionMutation,
    onSuccess: (data, variables) => {
      const deletedVersionId = variables.version_id;

      setVersions(prev => prev.filter(v => v.id !== deletedVersionId));

      if (currentVersionId === deletedVersionId) {
        const remainingVersions = versions.filter(v => v.id !== deletedVersionId);
        if (remainingVersions.length > 0) {
          switchToVersion(remainingVersions[0].id);
        }
      }

      successAlertRef.current?.show("Version deleted successfully!");
    },
    onError: (error, variables) => {

      // Handle specific error cases
      if (error?.response?.data?.err_id === 'CANNOT_DELETE_LAST_VERSION') {
        errorAlertRef.current?.show("Cannot delete the last remaining version");
      } else if (error?.response?.data?.err_id === 'VERSION_NOT_FOUND') {
        errorAlertRef.current?.show("Version not found or access denied");
      } else {
        errorAlertRef.current?.show("Failed to delete version. Please try again.");
      }
    }
  });

  const deleteVersion = (versionId: number) => {
    // Check if this is the last version
    if (versions.length <= 1) {
      errorAlertRef.current?.show("Cannot delete the last remaining version");
      return;
    }

    setVersionToDelete(versionId);
    setShowDeleteModal(true);
  };

  const handleConfirmDelete = async () => {
    if (versionToDelete !== null) {
      // Trigger the mutation
      deleteStatsPageVersionMut.mutate({ version_id: versionToDelete });

      // Reset state
      setShowDeleteModal(false);
      setVersionToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteModal(false);
    setVersionToDelete(null);
  };

  // To copy Script and div tag
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      successAlertRef.current?.show("Copied to clipboard!");
    }).catch(err => {
      errorAlertRef.current?.show("Failed to copy to clipboard.");
    });
  };

  // For responsive behavior - auto-collapse on small screens
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsSidebarCollapsed(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Navigate back from stats page
  const backToList = () => {
    navigate("/stats-page-table");
  };

  const injectTrialFooter = (htmlContent) => {
    return htmlContent;
  };

  const injectFooterWithJS = (): void => {
    const iframe = iframeRef.current;
    if (!iframe || !iframe.contentDocument) return;

    const doc = iframe.contentDocument;

    if (doc.body) {
      doc.body.setAttribute('spellcheck', 'false');
    }
    if (doc.documentElement) {
      doc.documentElement.setAttribute('spellcheck', 'false');
    }

    const waitForContentToLoad = (): Promise<void> => {
      return new Promise<void>((resolve) => {
        let attempts = 0;
        const maxAttempts = 50;

        const checkContent = () => {
          attempts++;

          const bodyContent = doc.body?.textContent?.trim() || '';
          const hasImages = doc.querySelectorAll('img').length > 0;
          const hasElements = doc.querySelectorAll('div, section, article, main, header, footer, nav, p, h1, h2, h3, h4, h5, h6').length > 0;

          const hasSubstantialContent = bodyContent.length > 100;
          const hasStructure = hasElements;

          if (hasSubstantialContent && hasStructure) {
            if (hasImages) {
              const images = doc.querySelectorAll('img');
              const imagePromises = Array.from(images).map((img: HTMLImageElement) => {
                return new Promise<void>((imgResolve) => {
                  if (img.complete) {
                    imgResolve();
                  } else {
                    img.onload = () => imgResolve();
                    img.onerror = () => imgResolve();
                    setTimeout(() => imgResolve(), 3000);
                  }
                });
              });

              Promise.all(imagePromises).then(() => {
                resolve();
              });
            } else {
              resolve();
            }
          } else if (attempts >= maxAttempts) {
            resolve();
          } else {
            setTimeout(checkContent, 200);
          }
        };

        checkContent();
      });
    };

    waitForContentToLoad().then(() => {
      setTimeout(() => {
        if (doc.querySelector('.custom-banner')) {
          return;
        }

        const footer = doc.createElement('div');
        footer.className = 'custom-banner';
        footer.contentEditable = 'false';
        footer.setAttribute('data-non-editable', 'true');
        footer.setAttribute('spellcheck', 'false');

        footer.innerHTML = `
                <div class="container">
                    <div class='button'>
                        <img src="https://abun.com/wp-content/uploads/2025/06/Ai-icon.svg" alt="AI Icon">
                        <span>Made with</span>
                        <a href="https://abun.com" target="_blank">Abun.com</a>
                    </div>
                </div>
            `;

        const style = doc.createElement('style');
        style.textContent = `
                /* Reset and spellcheck disable */
                * {
                    -webkit-spellcheck: false !important;
                    spellcheck: false !important;
                }
                
                /* Body adjustments for footer */
                body {
                    position: relative !important;
                    min-height: 100vh !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    padding-bottom: 80px !important;
                    box-sizing: border-box !important;
                }
                
                /* Main footer container with maximum specificity */
                .custom-banner {
                    all: initial !important;
                    position: absolute !important;
                    bottom: 0 !important;
                    left: 0 !important;
                    right: 0 !important;
                    top: auto !important;
                    width: 100% !important;
                    height: auto !important;
                    
                    background: #f3f4f6 !important;
                    background-color: #f3f4f6 !important;
                    border: none !important;
                    border-top: 1px solid #d1d5db !important;
                    border-radius: 0 !important;
                    padding: 20px 16px !important;
                    margin: 0 !important;
                    box-sizing: border-box !important;
                    z-index: 2147483647 !important;
                    
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    flex-direction: row !important;
                    flex-wrap: nowrap !important;
                    min-height: 60px !important;
                    max-height: none !important;
                    
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    font-size: 14px !important;
                    font-weight: 400 !important;
                    line-height: 1.4 !important;
                    color: #374151 !important;
                    text-align: center !important;
                    text-decoration: none !important;
                    text-transform: none !important;
                    
                    -webkit-user-select: none !important;
                    -moz-user-select: none !important;
                    -ms-user-select: none !important;
                    user-select: none !important;
                    pointer-events: auto !important;
                    
                    visibility: visible !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                    
                    box-shadow: none !important;
                    outline: none !important;
                    transform: none !important;
                    transition: none !important;
                    animation: none !important;
                    
                    float: none !important;
                    clear: both !important;
                    vertical-align: baseline !important;
                }
                
                /* Container within footer */
                .custom-banner .container {
                    all: initial !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    flex-direction: row !important;
                    flex-wrap: nowrap !important;
                    width: 100% !important;
                    height: auto !important;
                    max-width: none !important;
                    min-width: 0 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    background: none !important;
                    box-sizing: border-box !important;
                    position: relative !important;
                    z-index: auto !important;
                    overflow: visible !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                    float: none !important;
                    clear: none !important;
                    vertical-align: baseline !important;
                }
                
                /* Button styling */
                .custom-banner .button {
                    all: initial !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    flex-direction: row !important;
                    flex-wrap: nowrap !important;
                    gap: 8px !important;
                    
                    background: white !important;
                    background-color: white !important;
                    border: 1px solid #D2D2EB !important;
                    border-radius: 50px !important;
                    padding: 12px 18px !important;
                    margin: 0 !important;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
                    box-sizing: border-box !important;
                    
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    font-size: 14px !important;
                    font-weight: 400 !important;
                    line-height: 1.4 !important;
                    color: #000000 !important;
                    text-decoration: none !important;
                    text-align: center !important;
                    text-transform: none !important;
                    
                    cursor: pointer !important;
                    pointer-events: auto !important;
                    transition: all 0.2s ease !important;
                    
                    white-space: nowrap !important;
                    flex-shrink: 0 !important;
                    
                    position: relative !important;
                    z-index: auto !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                    
                    width: auto !important;
                    height: auto !important;
                    min-width: 0 !important;
                    min-height: 0 !important;
                    max-width: none !important;
                    max-height: none !important;
                    
                    float: none !important;
                    clear: none !important;
                    vertical-align: baseline !important;
                    
                    outline: none !important;
                    transform: none !important;
                    animation: none !important;
                }
                
                /* Button hover state */
                .custom-banner .button:hover {
                    transform: translateY(-1px) !important;
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
                    background: white !important;
                    background-color: white !important;
                    border: 1px solid #D2D2EB !important;
                }
                
                /* Button image styling */
                .custom-banner .button img {
                    all: initial !important;
                    width: 16px !important;
                    height: 16px !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    background: none !important;
                    background-color: transparent !important;
                    vertical-align: middle !important;
                    flex-shrink: 0 !important;
                    display: inline-block !important;
                    
                    position: relative !important;
                    z-index: auto !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                    
                    min-width: 16px !important;
                    min-height: 16px !important;
                    max-width: 16px !important;
                    max-height: 16px !important;
                    
                    float: none !important;
                    clear: none !important;
                    
                    outline: none !important;
                    transform: none !important;
                    animation: none !important;
                    transition: none !important;
                    
                    box-sizing: border-box !important;
                    object-fit: contain !important;
                    object-position: center !important;
                }
                
                /* Button span styling */
                .custom-banner .button span {
                    all: initial !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    background: none !important;
                    
                    color: #000000 !important;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    font-size: 14px !important;
                    font-weight: 400 !important;
                    line-height: 1.4 !important;
                    text-decoration: none !important;
                    text-align: center !important;
                    text-transform: none !important;
                    
                    display: inline !important;
                    white-space: nowrap !important;
                    
                    position: relative !important;
                    z-index: auto !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                    
                    width: auto !important;
                    height: auto !important;
                    min-width: 0 !important;
                    min-height: 0 !important;
                    max-width: none !important;
                    max-height: none !important;
                    
                    float: none !important;
                    clear: none !important;
                    vertical-align: baseline !important;
                    
                    outline: none !important;
                    transform: none !important;
                    animation: none !important;
                    transition: color 0.2s ease !important;
                    
                    box-sizing: border-box !important;
                }
                
                /* Button link styling */
                .custom-banner .button a {
                    all: initial !important;
                    color: #000000 !important;
                    text-decoration: underline !important;
                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                    font-size: 14px !important;
                    font-weight: 400 !important;
                    line-height: 1.4 !important;
                    text-align: center !important;
                    text-transform: none !important;
                    
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    background: none !important;
                    background-color: transparent !important;
                    
                    display: inline !important;
                    white-space: nowrap !important;
                    cursor: pointer !important;
                    pointer-events: auto !important;
                    transition: color 0.2s ease !important;
                    
                    position: relative !important;
                    z-index: auto !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    overflow: visible !important;
                    
                    width: auto !important;
                    height: auto !important;
                    min-width: 0 !important;
                    min-height: 0 !important;
                    max-width: none !important;
                    max-height: none !important;
                    
                    float: none !important;
                    clear: none !important;
                    vertical-align: baseline !important;
                    
                    outline: none !important;
                    transform: none !important;
                    animation: none !important;
                    
                    box-sizing: border-box !important;
                }
                
                /* Hover states */
                .custom-banner .button:hover a {
                    color: #2563eb !important;
                    text-decoration: underline !important;
                }
                
                .custom-banner .button:hover span {
                    color: #2563eb !important;
                }
                
                /* Mobile responsiveness */
                @media (max-width: 768px) {
                    body {
                        padding-bottom: 70px !important;
                    }
                    
                    .custom-banner {
                        padding: 16px 12px !important;
                        min-height: 50px !important;
                    }
                    
                    .custom-banner .button {
                        padding: 10px 16px !important;
                        font-size: 13px !important;
                    }
                    
                    .custom-banner .button span {
                        font-size: 13px !important;
                    }
                    
                    .custom-banner .button a {
                        font-size: 13px !important;
                    }
                    
                    .custom-banner .button img {
                        width: 14px !important;
                        height: 14px !important;
                        min-width: 14px !important;
                        min-height: 14px !important;
                        max-width: 14px !important;
                        max-height: 14px !important;
                    }
                }
                
                /* Ensure all elements have proper box-sizing */
                .custom-banner * {
                    box-sizing: border-box !important;
                }
                
                /* Force visibility on all footer elements */
                .custom-banner,
                .custom-banner *,
                .custom-banner .container,
                .custom-banner .button {
                    visibility: visible !important;
                    opacity: 1 !important;
                }
                
                .custom-banner .container {
                    display: flex !important;
                }
                
                .custom-banner .button {
                    display: flex !important;
                }
                
                .custom-banner .button span,
                .custom-banner .button a {
                    display: inline !important;
                }
                
                /* Ensure content doesn't overlap footer */
                body > *:not(.custom-banner) {
                    position: relative !important;
                    z-index: 1 !important;
                }
                
                body > div:not(.custom-banner),
                body > section:not(.custom-banner),
                body > main:not(.custom-banner),
                body > article:not(.custom-banner) {
                    padding-bottom: 20px !important;
                }
                
                /* Additional isolation rules */
                .custom-banner {
                    contain: layout style !important;
                    isolation: isolate !important;
                }
                
                /* Prevent any external styles from affecting footer */
                .custom-banner,
                .custom-banner * {
                    font-style: normal !important;
                    font-variant: normal !important;
                    text-indent: 0 !important;
                    text-shadow: none !important;
                    letter-spacing: normal !important;
                    word-spacing: normal !important;
                    direction: ltr !important;
                    writing-mode: horizontal-tb !important;
                    unicode-bidi: normal !important;
                }
            `;

        doc.head.appendChild(style);
        doc.body.appendChild(footer);

        // Event listeners to prevent editing
        footer.addEventListener('keydown', (e) => {
          e.preventDefault();
          e.stopPropagation();
        });

        footer.addEventListener('input', (e) => {
          e.preventDefault();
          e.stopPropagation();
        });

        footer.addEventListener('paste', (e) => {
          e.preventDefault();
          e.stopPropagation();
        });

        // Handle resize
        const handleResize = () => {
          if (footer.parentNode) {
            footer.style.setProperty('width', '100%', 'important');
          }
        };

        doc.defaultView?.addEventListener('resize', handleResize);

      }, 300);
    });
  };

  const getIframeContent = () => {
    if (!statsData?.html_content) return '';
    return injectTrialFooter(statsData.html_content);
  };

  const handleIframeLoad = () => {
    const iframeDoc = iframeRef.current?.contentDocument;
    if (!iframeDoc) return;

    // Check if this is a trial plan
    if (currentPlanName?.toLowerCase().trim() === "trial") {
      // Wait for document ready state
      const checkReadyState = () => {
        if (iframeDoc.readyState === 'complete') {
          injectFooterWithJS();
        } else {
          setTimeout(checkReadyState, 100);
        }
      };

      checkReadyState();
    }
  };

  // --------------------------- RENDER ---------------------------
  return (
    <div
      className={`statistic-page-generator-container ${showStatisticsPage ? 'full-stats-view' : 'generator-view'}`}
    >
      <div
        className={`card statistic-page-generator-header w-100 ${showStatisticsPage ? 'stats-view' : 'generator-view'}`}
      >
        {showStatisticsPage ? (
          <>
            <div className="left-header-section">
              <span
                className="back-btn"
                onClick={backToList}
              >
                <svg
                  className="back-btn"
                  width="30"
                  height="24"
                  viewBox="0 0 30 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" strokeOpacity="0.5" strokeWidth="3" />
                </svg>
              </span>

              <a href="/" onClick={(e) => { e.preventDefault(); navigate("/"); }}>
                <svg className="abun-logo" width="52" height="48" viewBox="0 0 52 48" >
                  <rect x="2.125" y="4.41016" width="47.9091" height="42.0909" rx="6.5" fill="black" stroke="black" stroke-width="3" />
                  <rect x="0.5" y="0.5" width="49.9091" height="44.0909" rx="7.5" fill="white" stroke="black" />
                  <path d="M40 37.3373H29.7561V34.7968C28.2195 36.6746 24.8618 38 21.4472 38C17.3496 38 12 35.2939 12 29.2189C12 22.5917 17.3496 20.714 21.4472 20.714C25.0325 20.714 28.2764 21.8185 29.7561 23.641V20.8797C29.7561 19.002 27.9919 17.5661 24.6341 17.5661C22.0732 17.5661 19.1707 18.5602 17.0081 20.1617L13.5366 14.0316C17.2358 11.1598 22.3577 10 26.5122 10C33.3415 10 40 12.3195 40 21.211V37.3373ZM25.7154 31.5385C27.3089 31.5385 29.0732 31.0414 29.7561 30.1026V28.6114C29.0732 27.6726 27.3089 27.1755 25.7154 27.1755C24.0081 27.1755 22.1301 27.7278 22.1301 29.3846C22.1301 31.0414 24.0081 31.5385 25.7154 31.5385Z" fill="black" />
                </svg>
              </a>

              <div className="Tabs">
                <div className="Tab active">
                  Stats Page
                </div>
              </div>
            </div>

            <div className="right-header-section">
              <div className="editor-controls">
                <button
                  className="sidebar-button save-btn"
                  onClick={handleSaveDirectHtmlChanges}
                  disabled={isGenerating || modifyAIStatsPageMut.isLoading || isUpdating}
                  style={{
                    opacity: (modifyAIStatsPageMut.isLoading || isUpdating) ? 0.6 : 1,
                    cursor: (modifyAIStatsPageMut.isLoading || isUpdating) ? 'not-allowed' : 'pointer'
                  }}
                >
                  {modifyAIStatsPageMut.isLoading ? "Saving..." : "Save"}
                </button>

                <svg
                  className={`collapse-button ${isSidebarCollapsed ? "" : "collapsed"}`}
                  onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                  width="20"
                  height="20"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{ cursor: 'pointer', marginLeft: '8px' }}
                >
                  <path fillRule="evenodd" clipRule="evenodd" d="M14 0H2C0.9 0 0 0.9 0 2V14C0 15.1 0.9 16 2 16H14C15.1 16 16 15.1 16 14V2C16 0.9 15.1 0 14 0ZM10 14.5H2C1.7 14.5 1.5 14.3 1.5 14V2C1.5 1.7 1.7 1.5 2 1.5H10V14.5ZM14.5 14C14.5 14.3 14.3 14.5 14 14.5H11.5V1.5H14C14.3 1.5 14.5 1.7 14.5 2V14Z" fill="#666" />
                </svg>
              </div>
            </div>
          </>
        ) : (
          <div className="left-header-section">
            <span
              className="back-btn"
              onClick={backToList}
            >
              <svg
                className="back-btn"
                width="30"
                height="24"
                viewBox="0 0 30 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" strokeOpacity="0.5" strokeWidth="3" />
              </svg>
            </span>
          </div>
        )}
      </div>

      <div className={`statistic-page-generator-content ${showStatisticsPage ? 'stats-view' : 'generator-view'}`}>
        {!showStatisticsPage && (
          <>
            <h1 className="statistic-page-generator-title">Statistic Page Generator</h1>
            <p className="statistic-page-generator-description">Create SEO-Optimized Stat Pages That Rank and Retain</p>
            <hr className="horizontal-line" />
          </>
        )}

        {!showStatisticsPage ? (
          <div>
            <div className="ca-content-row">
              <div className="ca-form-column">
                <form onSubmit={(e) => { e.preventDefault(); handleGenerateIdea(); }} style={{ marginTop: '-20px' }}>
                  <label className="ca-label">Keyword</label>
                  <input
                    className="ca-input"
                    type="text"
                    value={keyword}
                    onChange={(e) => setKeyword(e.target.value)}
                    placeholder="Enter keyword"
                    onBlur={() => {
                      if (keyword.trim()) {
                        handleGenerateIdea();
                      }
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleGenerateIdea();
                      }
                    }}
                    style={{ marginTop: '3px', textAlign: 'start' }}
                    disabled={isLoading}
                  />
                </form>

                <label className="ca-label">Stats Page Title</label>
                <input
                  className="ca-input"
                  type="text"
                  value={idea}
                  placeholder="Enter Stats Page Title or choose from the widget"
                  onChange={(e) => setIdea(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      handleGenerateIdea();
                    }
                  }}
                  onBlur={() => {
                    if (idea.trim()) {
                      handleGenerateIdea();
                    }
                  }}
                  style={{ textAlign: 'start' }}
                  disabled={isLoading}
                  required
                />

                <div className="Generate_button_large" style={{ position: 'relative', display: 'inline-block' }}>

                  <GenericButton
                    text={isGenerating || generateAIStatsPageMut.isLoading ? 'Generating Page...' : "Generate Page ➜"}
                    type={"primary"}
                    width={"219px"}
                    height={"40px"}
                    left={"7px"}
                    outlined={true}
                    disable={isLoading || !idea.trim() || isGenerating}
                    additionalClassList={["is-small", "more-rounded-borders"]}
                    clickHandler={handleGenerateStatisticPage}
                    style={{ fontSize: "1rem", borderRadius: "5px" }}
                  />

                </div>



              </div>

              <div className="ca-suggestion-box">
                <h3 className="ca-suggestion-title">Statistic Page Ideas based on keyword</h3>
                <div className="ca-suggestion-content">
                  {generateStatIdeasMut.isLoading ? (
                    <div className="ca-loading-state">
                      <div className="ca-loading-text">Generating Ideas, please wait...</div>
                    </div>
                  ) : ideas.length > 0 ? (
                    <ul className="ca-suggestion-list">
                      {ideas.map((title, idx) => (
                        <li
                          key={idx}
                          className="ca-suggestion-item"
                          data-tooltip-id="tooltip"
                          data-tooltip-content={keyword ? "Click to use Title" : "Please enter a keyword"}
                          onClick={() => {
                            if (keyword) {
                              handleIdeaClick(title, idx);
                            }
                          }}
                          style={{
                            fontWeight: idea === title ? '500' : '600',
                            paddingLeft: idea === title ? '13px' : '16px',
                            padding: '1px 16px',
                            borderRadius: '4px',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease'
                          }}
                        >
                          {title}
                        </li>
                      ))}

                      <Tooltip id="tooltip" place="bottom" />
                      <Tooltip id="textarea_btn" place="top" />
                      <Tooltip id="style_button" place="bottom" />
                    </ul>
                  ) : (
                    <div className="ca-empty-state">
                      <h3 className="ca-empty-state-title">
                        Enter Keyword to get Ideas
                      </h3>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="Generate_button">
              <GenericButton
                text={isGenerating || generateAIStatsPageMut.isLoading ? 'Generating Page...' : "Generate Page ➜"}
                type={"primary"}
                width={"219px"}
                height={"40px"}
                left={"7px"}
                outlined={true}
                disable={isLoading || !idea.trim() || isGenerating}
                additionalClassList={["is-small", "more-rounded-borders"]}
                clickHandler={handleGenerateStatisticPage}
                style={{ fontSize: "1rem", borderRadius: "5px" }}
              />
            </div>
          </div>
        ) : (

          <div className="stats-result-section-new">
            <div className="stats-preview-main">
              <div className="ai-preview-container">
                {isGenerating && !htmlContent ? (
                  <div className="loading-container" style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100%',
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px',
                    padding: '40px'
                  }}>
                    <Player
                      autoplay
                      loop
                      src="https://lottie.host/91a433df-05fa-4ab3-94b2-2c2a0a16a67f/2SoIqH8Kh3.json"
                      style={{ height: '300px', width: '300px' }}
                    >
                    </Player>
                    <h1 style={{ color: '#666', marginBottom: '10px', fontWeight: 'bolder' }}>
                      An Amazing Stats Page is being cooked for your site!
                    </h1>
                    <p style={{ color: '#888', textAlign: 'center', maxWidth: '300px' }}>
                      Creating your custom statistics page for "{statsData?.stats_topic}".
                      This may take a few moments.
                    </p>
                  </div>
                ) : (
                  <div
                    style={{
                      border: '1px solid #d1d5db',
                      borderRadius: '8px',
                      backgroundColor: 'white',
                      width: '100%',
                      height: '88vh',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      overflow: 'hidden'
                    }}
                  >
                    <div
                      style={{
                        backgroundColor: '#f3f4f6',
                        padding: '8px 12px',
                        borderBottom: '1px solid #d1d5db',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px',
                        minHeight: '40px',
                        position: 'sticky',
                        top: 0,
                        zIndex: 10,
                        flexShrink: 0
                      }}
                    >
                      <div style={{ display: 'flex', gap: '6px' }}>
                        <div
                          style={{
                            width: '12px',
                            height: '12px',
                            borderRadius: '50%',
                            backgroundColor: '#ff5f57'
                          }}
                        />
                        <div
                          style={{
                            width: '12px',
                            height: '12px',
                            borderRadius: '50%',
                            backgroundColor: '#ffbd2e'
                          }}
                        />
                        <div
                          style={{
                            width: '12px',
                            height: '12px',
                            borderRadius: '50%',
                            backgroundColor: '#28ca42'
                          }}
                        />
                      </div>

                      {/* URL Bar */}
                      <div
                        style={{
                          flex: 1,
                          backgroundColor: 'white',
                          border: '1px solid #d1d5db',
                          borderRadius: '4px',
                          padding: '6px 12px',
                          fontSize: '14px',
                          color: '#6b7280',
                          marginLeft: '8px'
                        }}
                      >
                        https://<span>{active_website_domain}</span>/<span>{statsId.replace(/-[a-f0-9]+$/, '')}</span>
                      </div>
                    </div>

                    {/* Navbar - Sticky */}
                    <div
                      style={{
                        backgroundColor: '#e5e7eb',
                        padding: '20px 16px',
                        borderBottom: '1px solid #d1d5db',
                        fontSize: '14px',
                        color: '#374151',
                        textAlign: 'center',
                        minHeight: '60px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        position: 'sticky',
                        top: '40px',
                        zIndex: 9,
                        flexShrink: 0
                      }}
                    >
                      Your Existing Website Navbar
                    </div>

                    {/* Main Scrollable Content Area */}

                    {/* Iframe Content */}
                    <iframe
                      ref={iframeRef}
                      srcDoc={getIframeContent()}
                      style={{
                        width: '100%',
                        height: '100%',
                        border: 'none',
                        backgroundColor: 'white',
                        display: 'block',
                        flex: 1
                      }}
                      title="Statistics Page Preview"
                      onLoad={handleIframeLoad}
                    />


                  </div>
                )}
              </div>
            </div>

            <div className={`stats-sidebar ${isSidebarCollapsed ? 'collapsed' : ''}`}>
              {/* Update Statistics Dropdown */}
              <div className="sidebar-section">
                <div
                  className={`sidebar-dropdown-header version-header ${updateTabOpen ? "active" : ""}`}
                  onClick={() => setUpdateTabOpen(!updateTabOpen)}
                >
                  <span><h6>What changes do you want in the Statistics Page?</h6></span>
                </div>

                {updateTabOpen && (
                  <div className="sidebar-dropdown-content">
                    <textarea
                      className="sidebar-textarea"
                      placeholder="Use different colors and add this..."
                      value={userInput}
                      onChange={(e) => setUserInput(e.target.value)}
                      disabled={isLoading || modifyAIStatsPageMut.isLoading}
                    />
                    <button
                      className="sidebar-button update-btn"
                      onClick={handleUpdateStatsPage}
                      disabled={isUpdating || !userInput.trim() || modifyAIStatsPageMut.isLoading}
                      style={{
                        opacity: (isUpdating || modifyAIStatsPageMut.isLoading) ? 0.6 : 1,
                        cursor: (isUpdating || modifyAIStatsPageMut.isLoading) ? 'not-allowed' : 'pointer',
                        position: 'relative'
                      }}
                    >
                      {isUpdating ? (
                        <span className="button-content">
                          <span className="spinner"></span>
                          Updating...
                        </span>
                      ) : (
                        "Update Statistics Page"
                      )}
                    </button>
                  </div>
                )}
              </div>

              {/* Version History Dropdown */}
              <div className="sidebar-section">
                <div
                  className={`sidebar-dropdown-header version-header ${versionsTabOpen ? "active" : ""}`}
                  onClick={() => setVersionsTabOpen(!versionsTabOpen)}
                >
                  <span><h6>Version History</h6></span>
                  <span className="version-count">
                    {versions.length}
                  </span>
                </div>
                {versionsTabOpen && (
                  <>
                    <div className="sidebar-dropdown-content">
                      {versions.length === 0 ? (
                        <p className="empty-versions">
                          No versions available for this page.
                        </p>
                      ) : (
                        <div className="versions-list-sidebar">
                          {versions.map((version, index) => (
                            <div
                              key={version.id}
                              className={`version-item-sidebar ${currentVersionId === version.id ? 'current-version' : ''}`}
                            >
                              <div className="version-header-sidebar">
                                <div className="version-info-sidebar">
                                  <div className="version-number-sidebar">
                                    <span>
                                      {index === versions.length - 1
                                        ? "Original"
                                        : `v${versions.length - index}`
                                      }
                                    </span>
                                  </div>
                                </div>
                                <div className="version-actions-sidebar" style={{
                                  display: 'flex',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  gap: '8px'
                                }}>
                                  {currentVersionId === version.id ? (
                                    <button className="sidebar-button small switch-btn" style={{ backgroundColor: '#10b981' }}>
                                      Current
                                    </button>
                                  ) : (
                                    <>
                                      <button
                                        className="sidebar-button small switch-btn"
                                        onClick={() => switchToVersion(version.id)}
                                      >
                                        Switch
                                      </button>
                                      {/* Only show delete button if it's not the original version */}
                                      {versions.length > 1 && index !== versions.length - 1 && (
                                        <button
                                          className="sidebar-button small danger"
                                          onClick={() => deleteVersion(version.id)}
                                        >
                                          Delete
                                        </button>
                                      )}
                                    </>
                                  )}
                                </div>
                              </div>
                              <div className="version-description-sidebar">
                                {version.changes_summary || 'No description available'}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Delete Confirmation Modal */}
                    <AbunModal
                      active={showDeleteModal}
                      headerText="Confirm Delete"
                      closeable={true}
                      closeableKey={true}
                      closeOnOutsideClick={false}
                      hideModal={handleCancelDelete}
                    >
                      <div>
                        <p>Are you sure you want to delete this version? This action cannot be undone.</p>
                        <div style={{
                          display: 'flex',
                          justifyContent: 'flex-end',
                          gap: '10px',
                          marginTop: '20px'
                        }}>
                          <button
                            className="button"
                            onClick={handleCancelDelete}
                          >
                            Cancel
                          </button>
                          <button
                            className="button is-danger"
                            onClick={handleConfirmDelete}
                          >
                            OK
                          </button>
                        </div>
                      </div>
                    </AbunModal>
                  </>
                )}

              </div>

              {/* Get Embed Code Dropdown */}
              {!isGenerating && !isUpdating && statsData && htmlContent && currentVersionId && (
                <div className="sidebar-section">
                  <div
                    className={`sidebar-dropdown-header version-header ${embedTabOpen ? "active" : ""}`}
                    onClick={() => {
                      setEmbedTabOpen(!embedTabOpen);
                    }}
                  >
                    <span><h6>Get Embed Code</h6></span>
                  </div>

                  {embedTabOpen && (
                    <div className="sidebar-dropdown-content">
                      {htmlContent && (
                        <div>

                          <div>
                            <div style={{ marginBottom: '15px' }}>
                              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                                Add this Script Tag to your head:
                              </label>
                              <textarea
                                className="sidebar-textarea embed-code"
                                readOnly
                                value={scriptTag}
                                style={{ minHeight: '90px', fontSize: '12px' }}
                              />
                              <button
                                className="sidebar-button copy-btn"
                                onClick={() => copyToClipboard(scriptTag)}
                                disabled={!scriptTag}
                              >
                                Copy
                              </button>
                            </div>
                            <div style={{ marginBottom: '15px' }}>
                              <label style={{ display: 'block', marginBottom: '5px', fontWeight: '500' }}>
                                Add this Div Tag in the body where you want to load:
                              </label>
                              <textarea
                                className="sidebar-textarea embed-code"
                                readOnly
                                value={divTag || '<!-- Div tag will be generated here -->'}
                                style={{ minHeight: '110px', fontSize: '12px' }}
                              />
                              <button
                                className="sidebar-button copy-btn"
                                onClick={() => copyToClipboard(divTag)}
                                disabled={!divTag}
                              >
                                Copy
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      <ErrorAlert ref={errorAlertRef} />
      <SuccessAlert ref={successAlertRef} />
    </div>
  );
};

export default withAdminAndProductionCheck(StatisticPage);